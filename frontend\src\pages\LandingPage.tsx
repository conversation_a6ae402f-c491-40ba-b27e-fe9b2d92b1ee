import React, { useState, useEffect } from 'react';
import { <PERSON> } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { <PERSON><PERSON>, Brain, TrendingUp, <PERSON><PERSON><PERSON>, <PERSON>mp<PERSON>, Bolt } from 'lucide-react';
import { <PERSON><PERSON> } from '../components/ui/button';
import { Card, CardContent } from '../components/ui/card';
import { Badge } from '../components/ui/badge';
import { InteractiveChartDisplay } from '../components/InteractiveChartDisplay';

// 动态网格高亮组件
const DynamicGridHighlight: React.FC = () => {
  const [highlights, setHighlights] = useState<Array<{
    id: string;
    type: 'horizontal' | 'vertical';
    position: number;
    opacity: number;
    duration: number;
  }>>([]);

  useEffect(() => {
    const createHighlight = () => {
      const id = Math.random().toString(36).substr(2, 9);
      const type = Math.random() > 0.5 ? 'horizontal' : 'vertical';
      const position = Math.random() * 100; // 0-100% 位置
      const duration = 500 + Math.random() * 1500; // 0.5-2秒随机持续时间

      const newHighlight = {
        id,
        type,
        position,
        opacity: 1,
        duration
      };

      setHighlights(prev => [...prev, newHighlight]);

      // 淡出动画
      setTimeout(() => {
        setHighlights(prev =>
          prev.map(h => h.id === id ? { ...h, opacity: 0 } : h)
        );
      }, duration * 0.3); // 30% 时间后开始淡出

      // 移除高亮
      setTimeout(() => {
        setHighlights(prev => prev.filter(h => h.id !== id));
      }, duration);
    };

    // 随机间隔创建高亮效果 (1-4秒)
    const scheduleNext = () => {
      const delay = 1000 + Math.random() * 3000;
      setTimeout(() => {
        createHighlight();
        scheduleNext();
      }, delay);
    };

    scheduleNext();
  }, []);

  return (
    <div className="absolute inset-0 pointer-events-none overflow-hidden">
      {highlights.map(highlight => (
        <div
          key={highlight.id}
          className={`absolute transition-opacity duration-1000 ease-out ${
            highlight.type === 'horizontal'
              ? 'left-0 right-0 h-px'
              : 'top-0 bottom-0 w-px'
          }`}
          style={{
            [highlight.type === 'horizontal' ? 'top' : 'left']: `${highlight.position}%`,
            opacity: highlight.opacity,
            background: 'linear-gradient(90deg, transparent 0%, rgba(0, 245, 255, 0.6) 20%, rgba(0, 245, 255, 0.8) 50%, rgba(0, 245, 255, 0.6) 80%, transparent 100%)',
            boxShadow: '0 0 10px rgba(0, 245, 255, 0.4)',
            ...(highlight.type === 'vertical' && {
              background: 'linear-gradient(180deg, transparent 0%, rgba(0, 245, 255, 0.6) 20%, rgba(0, 245, 255, 0.8) 50%, rgba(0, 245, 255, 0.6) 80%, transparent 100%)'
            })
          }}
        />
      ))}
    </div>
  );
};

const LandingPage: React.FC = () => {
  const { t } = useTranslation();
  const [activeFeature, setActiveFeature] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setActiveFeature((prev) => (prev + 1) % 4);
    }, 3000);
    return () => clearInterval(interval);
  }, []);

  const features = [
    {
      icon: Brain,
      title: t('landing.features.ai.title'),
      description: t('landing.features.ai.description'),
    },
    {
      icon: Compass,
      title: t('landing.features.yijing.title'),
      description: t('landing.features.yijing.description'),
    },
    {
      icon: TrendingUp,
      title: t('landing.features.trend.title'),
      description: t('landing.features.trend.description'),
    },
    {
      icon: Bolt,
      title: t('landing.features.highFreq.title'),
      description: t('landing.features.highFreq.description'),
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-cyber-bg via-cyber-bg to-cyber-card relative overflow-hidden">
      {/* Animated Background Elements - 网格背景 */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Grid Pattern */}
        <div className="absolute inset-0 opacity-5">
          <svg width="100%" height="100%">
            <defs>
              <pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse">
                <path d="M 50 0 L 0 0 0 50" fill="none" stroke="rgba(0, 245, 255, 0.3)" strokeWidth="1" />
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#grid)" />
          </svg>
        </div>
      </div>

      {/* Hero Section */}
      <section className="relative px-6 pt-40 pb-2 min-h-screen flex items-start">
        <div className="max-w-7xl mx-auto w-full">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            {/* Left Column - Content */}
            <div className="space-y-8 animate-fade-in">
              {/* Badge */}
              <Badge className="bg-cyber-cyan/20 text-cyber-cyan border-cyber-cyan/30 px-4 py-2 text-sm font-mono">
                <Sparkles className="h-4 w-4 mr-2" />
                {t('landing.badge')}
              </Badge>

              {/* Main Heading */}
              <div className="space-y-6">
                <h1 className="text-5xl md:text-7xl font-bold leading-tight">
                  <span className="text-cyber-text">{t('landing.hero.title1')}</span>
                  <br />
                  <span
                    className="font-bold"
                    style={{
                      background: "linear-gradient(90deg, #00f5ff 0%, #0066ff 50%, #b537f2 100%)",
                      WebkitBackgroundClip: "text",
                      WebkitTextFillColor: "transparent",
                      backgroundClip: "text",
                      display: "inline-block",
                    }}
                  >
                    {t('landing.hero.title2')}
                  </span>
                </h1>

                <p className="text-xl md:text-2xl text-cyber-muted leading-relaxed max-w-2xl">
                  {t('landing.hero.description')}
                </p>
              </div>

              {/* CTA Button - 只保留一个按钮 */}
              <div className="flex justify-center pt-8">
                <Button
                  asChild
                  className="h-16 px-12 bg-gradient-to-r from-cyber-cyan to-cyber-blue hover:from-cyber-cyan/90 hover:to-cyber-blue/90 text-black font-bold text-xl rounded-2xl shadow-2xl hover:shadow-[0_0_40px_rgba(0,245,255,0.5)] transition-all duration-300 font-mono group"
                >
                  <Link to="/register">
                    <Zap className="h-6 w-6 mr-3 group-hover:animate-pulse" />
                    {t('landing.cta')}
                  </Link>
                </Button>
              </div>
            </div>

            {/* Right Column - Interactive Charts with 聚焦色彩效果 */}
            <div className="relative">
              {/* 聚焦聚光灯效果 - 从图表中心向外扩散 */}
              <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-radial from-cyan-400/20 via-cyan-400/10 to-transparent rounded-full blur-xl animate-pulse"></div>

              {/* 更精准的聚焦光束 */}
              <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-radial from-purple-400/15 via-transparent to-transparent rounded-full blur-lg"></div>

              <div className="relative z-10">
                <InteractiveChartDisplay />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="relative px-6 py-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-cyber-text mb-6 font-sans">{t('landing.features.title')}</h2>
            <p className="text-xl text-cyber-muted max-w-3xl mx-auto">
              {t('landing.features.subtitle')}
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <Card
                key={index}
                className={`bg-cyber-card/40 backdrop-blur-xl border transition-all duration-500 hover:scale-105 cursor-pointer ${
                  activeFeature === index
                    ? "border-cyber-cyan/70 shadow-[0_0_30px_rgba(0,245,255,0.3)]"
                    : "border-cyber-border/30 hover:border-cyber-cyan/50"
                }`}
                onClick={() => setActiveFeature(index)}
              >
                <CardContent className="p-8 text-center space-y-4">
                  <div
                    className={`w-16 h-16 mx-auto rounded-2xl flex items-center justify-center transition-all duration-300 ${
                      activeFeature === index
                        ? "bg-gradient-to-br from-cyber-cyan to-cyber-blue shadow-lg"
                        : "bg-cyber-border/30"
                    }`}
                  >
                    <feature.icon className={`h-8 w-8 ${activeFeature === index ? "text-black" : "text-cyber-cyan"}`} />
                  </div>
                  <h3 className="text-xl font-bold text-cyber-text font-sans">{feature.title}</h3>
                  <p className="text-cyber-muted text-sm leading-relaxed">{feature.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Disclaimer Section */}
      <section className="relative px-6 py-20">
        <div className="max-w-5xl mx-auto text-center">
          <Card className="bg-cyber-card/20 backdrop-blur-xl border border-cyber-border/50 rounded-3xl shadow-lg relative overflow-hidden">
            {/* Subtle animated border glow */}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-cyber-border/30 to-transparent animate-pulse rounded-3xl"></div>

            <CardContent className="p-12 relative z-10">
              <p className="leading-relaxed font-mono text-lg md:text-xl font-bold max-w-4xl mx-auto text-cyber-cyan">
                {t('landing.disclaimer')}
              </p>

              {/* Decorative elements */}
              <div className="flex justify-center mt-6 space-x-2">
                <div className="w-2 h-2 bg-cyber-cyan/60 rounded-full animate-pulse"></div>
                <div
                  className="w-2 h-2 bg-cyber-cyan/40 rounded-full animate-pulse"
                  style={{ animationDelay: "0.2s" }}
                ></div>
                <div
                  className="w-2 h-2 bg-cyber-cyan/60 rounded-full animate-pulse"
                  style={{ animationDelay: "0.4s" }}
                ></div>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>
    </div>
  );
};

export default LandingPage;